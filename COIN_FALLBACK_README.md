# Tính năng Fallback Coin từ PowerSync sang Firebase

## Tổng quan

Tính năng này cho phép ứng dụng tự động chuyển sang sử dụng Firebase để quản lý coin khi PowerSync hoặc backend b<PERSON> sậ<PERSON>, đả<PERSON> bảo ứng dụng vẫn hoạt động bình thường mà không cần thay đổi UI.

## C<PERSON><PERSON> thức hoạt động

### 1. Trạng thái kết nối
- **PowerSync Connected**: Tr<PERSON><PERSON> thá<PERSON> bình thườ<PERSON>, sử dụng PowerSync cho tất cả operations
- **Firebase Fallback**: Khi PowerSync bị lỗi, chuy<PERSON><PERSON> sang sử dụng Firebase (hoàn toàn trong background)

### 2. Phát hiện lỗi kết nối
- Lỗi kết nối được phát hiện trong `MyConnector.fetchCredentials()`
- Lỗi cập nhật dữ liệu được phát hiện trong `CoinViewModel.updateCoinBalance()`
- Callback `onConnectionError` được gọi để thông báo lỗi

### 3. <PERSON><PERSON>ển đổi tự động (Background)
Khi phát hiện lỗi PowerSync:
1. Cập nhật trạng thái `isPowerSyncConnected = false`
2. Bật chế độ `isUsingFirebaseFallback = true`
3. Đồng bộ coin từ Firebase
4. Tự động retry kết nối PowerSync sau 30 giây

### 4. Đồng bộ dữ liệu
- **Dual Storage**: Coin được lưu đồng thời trên PowerSync và Firebase
- **Firebase Priority**: Khi PowerSync lỗi, Firebase trở thành nguồn dữ liệu chính
- **Auto Retry**: Tự động thử kết nối lại PowerSync định kỳ
- **Seamless Recovery**: Khi PowerSync khôi phục, dữ liệu được đồng bộ lại tự động

## Các file đã thay đổi

### 1. CoinViewModel.kt
- Thêm trạng thái kết nối: `isPowerSyncConnected`, `isUsingFirebaseFallback`
- Method `handlePowerSyncError()`: Xử lý lỗi và chuyển sang Firebase
- Method `retryPowerSyncConnection()`: Thử kết nối lại PowerSync
- Method `syncCoinFromFirebase()`: Đồng bộ coin từ Firebase
- Cải thiện `updateCoinBalance()`: Dual storage và error handling

### 2. MyConnector.kt
- Thêm callback `onConnectionError` để thông báo lỗi
- Cải thiện error handling trong `fetchCredentials()` và `getPowerSyncToken()`

### 3. AppDatabase.kt
- Cập nhật `connectToBackend()` để hỗ trợ error callback

### 4. MainEntryCompose.kt
- Loại bỏ các thay đổi UI không cần thiết
- Giữ nguyên giao diện hiện tại

### 5. FirestoreUtils.kt
- Thêm method `getCoinsForUser()`: Lấy coin từ Firebase
- Cải thiện error handling và logging

### 6. GoogleAuthUiClient.kt
- Cập nhật để sử dụng error callback khi kết nối PowerSync

## Cách sử dụng

### Theo dõi trạng thái kết nối (nếu cần)
```kotlin
val isPowerSyncConnected = coinViewModel.isPowerSyncConnected.collectAsState()
val isUsingFirebaseFallback = coinViewModel.isUsingFirebaseFallback.collectAsState()
```

### Tính năng hoạt động tự động
- Không cần can thiệp thủ công
- Tự động chuyển đổi khi có lỗi
- Tự động retry kết nối PowerSync
- Coin vẫn được cập nhật bình thường

## Lợi ích

1. **Độ tin cậy cao**: Ứng dụng vẫn hoạt động khi backend sập
2. **Trải nghiệm người dùng tốt**: Không bị gián đoạn, không thay đổi UI
3. **Đồng bộ tự động**: Dữ liệu được đồng bộ khi kết nối khôi phục
4. **Hoạt động trong nền**: Người dùng không cần biết về việc chuyển đổi
5. **Auto Recovery**: Tự động khôi phục kết nối PowerSync

## Testing

Chạy test để kiểm tra tính năng:
```bash
./gradlew test --tests="CoinFallbackTest"
```

## Lưu ý

- Firebase được sử dụng như backup storage, không thay thế hoàn toàn PowerSync
- Lịch sử giao dịch có thể bị mất khi PowerSync offline (cần implement backup cho transaction history)
- Cần đảm bảo Firebase rules cho phép read/write coin data
- Nên monitor và log các lỗi kết nối để phân tích và cải thiện
