package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.dlog
import amobi.module.openai.api.logging.Logger
import android.os.Handler
import android.os.Looper
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.powersync.PowerSyncDatabase
import com.powersync.connectors.PowerSyncBackendConnector
import com.powersync.connectors.PowerSyncCredentials
import com.powersync.db.crud.CrudEntry
import com.powersync.db.crud.UpdateType
import com.powersync.db.runWrappedSuspending
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit

/**
 * Data classes cho API responses
 */
@Serializable
data class PowerSyncTokenResponse(
    val powerSyncUrl: String,
    val token: String,
    val userId: String,
    val expiresAt: Long
)

@Serializable
data class ApiDataRequest(
    val table: String,
    val data: JsonElement
)

class MyConnector : PowerSyncBackendConnector() {

    companion object {
        private const val BACKEND_URL = "http://192.168.1.90:3000" // Thay thế bằng URL backend thực
        private const val TIMEOUT_SECONDS = 30L
    }

    private var errorCode: String? = null

    private object PostgresFatalCodes {
        // Using Regex patterns for Postgres error codes
        private val FATAL_RESPONSE_CODES =
            listOf(
                // Class 22 — Data Exception
                "^22...".toRegex(),
                // Class 23 — Integrity Constraint Violation
                "^23...".toRegex(),
                // INSUFFICIENT PRIVILEGE
                "^42501$".toRegex(),
            )

        fun isFatalError(code: String): Boolean =
            FATAL_RESPONSE_CODES.any { pattern ->
                pattern.matches(code)
            }
    }

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .build()

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    /**
     * Chuyển đổi Map<String, String?> thành JsonElement
     */
    private fun mapToJsonElement(data: Map<String, String?>): JsonElement {
        return buildJsonObject {
            data.forEach { (key, value) ->
                if (value != null) {
                    put(key, value)
                } else {
                    put(key, "")
                }
            }
        }
    }

    /**
     * Lấy PowerSync token từ backend
     */
    private suspend fun getPowerSyncToken(): PowerSyncTokenResponse? {
        return withContext(Dispatchers.IO) {
            try {
                val user = FirebaseAuth.getInstance().currentUser
                var userId = ""
                var firebaseAuthToken = ""

                if (user != null) {
                    // Lấy token từ Firebase
                    firebaseAuthToken = user.getIdToken(false).await().token ?: ""
                    if (firebaseAuthToken == "") {
                        debugLog("powerSync Firebase token không hợp lệ hoặc hết hạn")
                        return@withContext null
                    }
                    userId = user.uid
                } else {
                    throw IllegalStateException("Firebase user không tồn tại")
                    return@withContext null
                }
                debugLog("powerSync userId: $userId, firebaseAuthToken: $firebaseAuthToken")

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/auth/token2")
                    .addHeader("User-Id", userId)
                    .addHeader("Firebase-Auth-Token", firebaseAuthToken)
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    val responseBody = response.body.string()
                    debugLog("powerSync responseBody: $responseBody")
                    responseBody.let {
                        json.decodeFromString<PowerSyncTokenResponse>(it)
                    }
                } else {
                    debugLog("powerSync Lỗi khi lấy PowerSync token: ${response.code}")
                    null
                }
            } catch (e: Exception) {
                debugLog("powerSync Exception khi lấy PowerSync token: ${e.message}")
                null
            }
        }
    }

    override suspend fun fetchCredentials(): PowerSyncCredentials {
        debugLog("powerSync fetchCredentials")

        // Thử lấy token từ backend trước
        val tokenResponse = getPowerSyncToken()

        debugLog("powerSync tokenResponse: $tokenResponse")

        MixedUtils.showToast("userId tokenResponse: ${tokenResponse?.userId}")
        return if (tokenResponse != null) {
            debugLog("powerSync Sử dụng token từ backend")
            PowerSyncCredentials(
                endpoint = tokenResponse.powerSyncUrl,
                token = tokenResponse.token,
                userId = tokenResponse.userId,
            )
        } else {
            PowerSyncCredentials(
                endpoint = "http://192.168.1.19:8080",
                token = "demo-token-for-offline-development",
            )
        }
    }

    /**
     * Upsert dữ liệu (tạo mới hoặc cập nhật toàn bộ)
     */
    private suspend fun upsertData(table: String, data: Map<String, String?>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                debugLog("powerSync upsertData table: $table, data: $data")
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(data))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                debugLog("powerSync upsertData requestBody: $requestBody")
                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .put(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi upsert data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception upsert data: ${e.message}")
                false
            }
        }
    }

    /**
     * Cập nhật dữ liệu (chỉ các trường được chỉ định)
     */
    private suspend fun updateData(table: String, data: Map<String, String?>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(data))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .patch(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi update data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception update data: ${e.message}")
                false
            }
        }
    }

    /**
     * Xóa dữ liệu
     */
    private suspend fun deleteData(table: String, id: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(mapOf("id" to id)))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .delete(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi delete data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception delete data: ${e.message}")
                false
            }
        }
    }

    override suspend fun uploadData(database: PowerSyncDatabase) {
        return withContext(Dispatchers.IO) {
            val transaction = database.getCrudBatch() ?: return@withContext

            var lastEntry: CrudEntry? = null
            try {
                for (entry in transaction.crud) {
                    val table = entry.table
                    val id = entry.id
                    // Đảm bảo data luôn có id
                    val data = (entry.opData ?: emptyMap()).toMutableMap().apply {
                        put("id", id)
                        put("user_id", PrefAssist.getString(PrefConst.USER_ID))
                    }

                    val success = when (entry.op) {
                        UpdateType.PUT -> {
                            debugLog("powerSync uploadData PUT cho table: $table, id: $id")
                            upsertData(table, data)
                        }

                        UpdateType.PATCH -> {
                            debugLog("powerSync uploadData PATCH cho table: $table, id: $id")
                            updateData(table, data)
                        }

                        UpdateType.DELETE -> {
                            debugLog("powerSync uploadData DELETE cho table: $table, id: $id")
                            deleteData(table, id)
                        }
                    }

                    if (success) {
                        debugLog("powerSync Thành công ${entry.op} cho $table:$id")
                        transaction.complete(null)
                    } else {
                        debugLog("powerSync Thất bại ${entry.op} cho $table:$id")
                    }
                }

                // Đánh dấu batch đã được xử lý
                try {
                    debugLog("powerSync Calling batch.complete() to mark batch as processed")
                } catch (e: Exception) {
                    debugLog("powerSync Exception when calling batch.complete(): ${e.message}")
                }
            } catch (e: Exception) {
                if (errorCode != null && PostgresFatalCodes.isFatalError(errorCode.toString())) {
                    /**
                     * Instead of blocking the queue with these errors,
                     * discard the (rest of the) transaction.
                     *
                     * Note that these errors typically indicate a bug in the application.
                     * If protecting against data loss is important, save the failing records
                     * elsewhere instead of discarding, and/or notify the user.
                     */
                    dlog("Data upload error: ${e.message}")
                    dlog("Discarding entry: $lastEntry")
                    transaction.complete(null)
                    return@withContext
                }

                dlog("Data upload error - retrying last entry: $lastEntry, $e")
                throw e
            }
        }
    }
}