package com.amobilab.ezmath.ai.app

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.debugLogTrace
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryEntityPowerSync
import com.amobilab.ezmath.ai.data.db.powerSync.TransactionType
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

import com.google.android.gms.auth.api.identity.Identity
import com.google.firebase.auth.FirebaseAuth

@Singleton
class CoinViewModel @Inject constructor() : ViewModel() {

    val maxTooltipDisplaysScanTab = 2

    val maxTooltipDisplaysChatCompose = 8

    // Trạng thái kết nối PowerSync
    private val _isPowerSyncConnected = MutableStateFlow(true)
    val isPowerSyncConnected = _isPowerSyncConnected.asStateFlow()

    // Trạng thái sử dụng Firebase fallback
    private val _isUsingFirebaseFallback = MutableStateFlow(false)
    val isUsingFirebaseFallback = _isUsingFirebaseFallback.asStateFlow()


    init {
        viewModelScope.launch {
            try {
                AppDatabase.Companion
                    .getInstance()
                    .getUserRepository()
                    .watchCoin()
                    .collect {
                        val newCoinTotal = it
                        if (newCoinTotal == 0L) return@collect

                        // Cập nhật trạng thái kết nối thành công
                        _isPowerSyncConnected.value = true
                        _isUsingFirebaseFallback.value = false

                        if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, 0) != 0L) {
                            debugLog("Coin balance updated with temp value: $newCoinTotal")
                            AppDatabase.getInstance().getUserRepository().updateCoin(
                                newCoinTotal + PrefAssist.getLong(
                                    PrefConst.TOTAL_COIN_BALANCE_TEMP,
                                    0
                                )
                            )
                            setCoinBalance(
                                newCoinTotal + PrefAssist.getLong(
                                    PrefConst.TOTAL_COIN_BALANCE_TEMP,
                                    0
                                )
                            )
                            PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, 0)
                        } else {
                            if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE_OLD, 0) == 0L) {
                                setCoinBalance(newCoinTotal)
                            } else {
                                setCoinBalance(
                                    PrefAssist.getLong(
                                        PrefConst.TOTAL_COIN_BALANCE_OLD,
                                        0
                                    )
                                )
                                if (FirebaseAuth.getInstance().currentUser?.uid != null) {
                                    AppDatabase.getInstance().getUserRepository().updateCoin(
                                        newCoinTotal + PrefAssist.getLong(
                                            PrefConst.TOTAL_COIN_BALANCE_OLD,
                                            0
                                        )
                                    )
                                }
                                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_OLD, 0)
                            }
                        }
                        debugLogTrace("Coin balance updated: $newCoinTotal")
                    }
                debugLog("Connected to PowerSync")
            } catch (e: Exception) {
                debugLog("PowerSync connection error: ${e.message}")
                handlePowerSyncError()
            }
        }

        viewModelScope.launch {
            try {
                AppDatabase.Companion
                    .getInstance()
                    .getUserRepository()
                    .watchIsDelete()
                    .collect {
                        if (it == 1L) {
                            // logout
                            _isDelete.value = 1L
                        } else {
                            _isDelete.value = 0L
                        }
                    }
            } catch (e: Exception) {
                debugLog("Connection error: ${e.message}")
            }
        }
    }

    private val _isShowCoinInsufficientDialog = MutableStateFlow(false)
    val isShowCoinInsufficientDialog = _isShowCoinInsufficientDialog.asStateFlow()

    fun showInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = true
    }

    fun hideInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = false
    }

    val appThemeMode: MutableLiveData<AppThemeMode> by lazy {
        val savedAppThemeMode = PrefAssist.getString(
            PrefConst.APP_THEME_MODE,
            AppThemeMode.SYSTEM.name
        )
        MutableLiveData<AppThemeMode>(AppThemeMode.valueOf(savedAppThemeMode))
    }


    fun setAppTheme(appThemeMode: AppThemeMode) {
        this.appThemeMode.value = appThemeMode
        PrefAssist.setString(PrefConst.APP_THEME_MODE, appThemeMode.name)
    }


    private val _coinTotal = MutableStateFlow(PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0))
    val coinTotal = _coinTotal.asStateFlow()

    private val _isDelete = MutableStateFlow(0L)
    val isDelete = _isDelete.asStateFlow()

    fun setCoinBalance(newCoinTotal: Long) {
        _coinTotal.value = newCoinTotal
        PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, newCoinTotal)

        if (FirebaseAuth.getInstance().currentUser?.uid == null) {
            PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, newCoinTotal)
        }

        debugLog("saveCoinTotal: $newCoinTotal")
    }

    fun updateCoinBalance(amount: Long, description: String) {
        val coinTotal = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
        viewModelScope.launch(Dispatchers.IO) {
            if (FirestoreUtils.syncCoinsInterval % 10 != 0L) {
                FirestoreUtils.syncCoinsFirestore()
            }
            FirestoreUtils.syncCoinsInterval++

            withContext(Dispatchers.Main) {
                setCoinBalance(coinTotal + amount)
            }

            val user = FirebaseAuth.getInstance().currentUser
            if (user?.uid != null){
                // Thử cập nhật PowerSync trước
                try {
                    if (_isPowerSyncConnected.value) {
                        AppDatabase
                            .getInstance()
                            .getUserRepository()
                            .updateCoin(coinTotal + amount)
                    } else {
                        // Nếu PowerSync không kết nối, chỉ dùng Firebase
                        debugLog("PowerSync not connected, using Firebase fallback for coin update")
                    }
                } catch (e: Exception) {
                    debugLog("PowerSync update failed: ${e.message}, switching to Firebase fallback")
                    handlePowerSyncError()
                }

                // Luôn cập nhật Firebase như backup
                FirestoreUtils.updateCoinsForUser(user.uid, coinTotal + amount)
            }

            debugLog( "updateCoinBalance: $amount, description: $description")

            // Thử lưu lịch sử giao dịch
            try {
                if (_isPowerSyncConnected.value) {
                    AppDatabase
                        .getInstance()
                        .getCoinHistoryRepository()
                        .insertTransaction(
                            CoinHistoryEntityPowerSync(
                                type = if (amount > 0) TransactionType.EARN else TransactionType.SPEND,
                                amount = abs(amount.toInt()),
                                date = System.currentTimeMillis(),
                                description = description
                            )
                        )
                }
            } catch (e: Exception) {
                debugLog("Failed to save transaction history to PowerSync: ${e.message}")
                // Có thể lưu vào Firebase hoặc local storage như fallback
            }
        }
    }


    private val _freeChat = MutableStateFlow(PrefAssist.getInt(PrefConst.FREE_CHAT))
    val freeChat = _freeChat.asStateFlow()

    fun countdownFreeChat() {
        val newFreeChat = PrefAssist.getInt(PrefConst.FREE_CHAT) - 1
        _freeChat.value = newFreeChat
    }

    /**
     * Xử lý lỗi kết nối PowerSync và chuyển sang Firebase fallback
     */
    private fun handlePowerSyncError() {
        debugLog("Switching to Firebase fallback mode")
        _isPowerSyncConnected.value = false
        _isUsingFirebaseFallback.value = true

        // Khởi tạo Firebase fallback mode
        initializeFirebaseFallback()
    }

    /**
     * Khởi tạo chế độ Firebase fallback
     */
    private fun initializeFirebaseFallback() {
        viewModelScope.launch {
            try {
                val user = FirebaseAuth.getInstance().currentUser
                if (user?.uid != null) {
                    // Đồng bộ coin từ Firebase
                    syncCoinFromFirebase(user.uid)

                    // Bắt đầu theo dõi thay đổi từ Firebase
                    startFirebaseWatcher(user.uid)
                }
            } catch (e: Exception) {
                debugLog("Failed to initialize Firebase fallback: ${e.message}")
            }
        }
    }

    /**
     * Đồng bộ coin từ Firebase
     */
    private suspend fun syncCoinFromFirebase(userId: String) {
        try {
            // Lấy coin từ Firebase và cập nhật local
            val firebaseCoins = FirestoreUtils.getCoinsForUser(userId)
            if (firebaseCoins != null) {
                setCoinBalance(firebaseCoins)
                debugLog("Synced coin from Firebase successfully: $firebaseCoins")
            } else {
                // Nếu không có dữ liệu trên Firebase, sử dụng coin local hiện tại
                val localCoins = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0)
                FirestoreUtils.updateCoinsForUser(userId, localCoins)
                debugLog("No Firebase data found, uploaded local coins: $localCoins")
            }
        } catch (e: Exception) {
            debugLog("Failed to sync coin from Firebase: ${e.message}")
        }
    }

    /**
     * Bắt đầu theo dõi thay đổi từ Firebase (có thể implement sau)
     */
    private fun startFirebaseWatcher(userId: String) {
        // TODO: Implement Firebase real-time listener nếu cần
        debugLog("Firebase watcher started for user: $userId")
    }

    /**
     * Thử kết nối lại PowerSync (tự động retry)
     */
    private fun retryPowerSyncConnection() {
        viewModelScope.launch {
            try {
                debugLog("Attempting to reconnect to PowerSync...")
                AppDatabase.getInstance().connectToBackend { error ->
                    debugLog("PowerSync reconnection error: $error")
                }

                // Kiểm tra kết nối bằng cách thử lấy dữ liệu
                val testCoin = AppDatabase.getInstance().getUserRepository().getCoin()

                // Nếu thành công, cập nhật trạng thái
                _isPowerSyncConnected.value = true
                _isUsingFirebaseFallback.value = false
                debugLog("PowerSync reconnected successfully")

            } catch (e: Exception) {
                debugLog("PowerSync reconnection failed: ${e.message}")
                // Vẫn giữ Firebase fallback mode
            }
        }
    }

}