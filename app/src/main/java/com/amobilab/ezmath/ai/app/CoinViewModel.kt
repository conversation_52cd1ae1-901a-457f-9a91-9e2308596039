package com.amobilab.ezmath.ai.app

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.debugLogTrace
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryEntityPowerSync
import com.amobilab.ezmath.ai.data.db.powerSync.TransactionType
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

import com.google.android.gms.auth.api.identity.Identity
import com.google.firebase.auth.FirebaseAuth

@Singleton
class CoinViewModel @Inject constructor() : ViewModel() {

    val maxTooltipDisplaysScanTab = 2

    val maxTooltipDisplaysChatCompose = 8

    // Trạng thái kết nối PowerSync
    private val _isPowerSyncConnected = MutableStateFlow(true)
    val isPowerSyncConnected = _isPowerSyncConnected.asStateFlow()

    // Trạng thái sử dụng Firebase fallback
    private val _isUsingFirebaseFallback = MutableStateFlow(false)
    val isUsingFirebaseFallback = _isUsingFirebaseFallback.asStateFlow()


    init {
        viewModelScope.launch {
            try {
                AppDatabase.Companion
                    .getInstance()
                    .getUserRepository()
                    .watchCoin()
                    .collect {
                        val newCoinTotal = it
                        if (newCoinTotal == 0L) return@collect
                        if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, 0) != 0L) {
                            debugLog("Coin balance updated with temp value: $newCoinTotal")
                            AppDatabase.getInstance().getUserRepository().updateCoin(
                                newCoinTotal + PrefAssist.getLong(
                                    PrefConst.TOTAL_COIN_BALANCE_TEMP,
                                    0
                                )
                            )
                            setCoinBalance(
                                newCoinTotal + PrefAssist.getLong(
                                    PrefConst.TOTAL_COIN_BALANCE_TEMP,
                                    0
                                )
                            )
                            PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, 0)
                        } else {
                            if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE_OLD, 0) == 0L) {
                                setCoinBalance(newCoinTotal)
                            } else {
                                setCoinBalance(
                                    PrefAssist.getLong(
                                        PrefConst.TOTAL_COIN_BALANCE_OLD,
                                        0
                                    )
                                )
                                if (FirebaseAuth.getInstance().currentUser?.uid != null) {
                                    AppDatabase.getInstance().getUserRepository().updateCoin(
                                        newCoinTotal + PrefAssist.getLong(
                                            PrefConst.TOTAL_COIN_BALANCE_OLD,
                                            0
                                        )
                                    )
                                }
                                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_OLD, 0)
                            }
                        }
                        debugLogTrace("Coin balance updated: $newCoinTotal")
                    }
                debugLog("Connected to PowerSync")
            } catch (e: Exception) {
                debugLog("Connection error: ${e.message}")
            }
        }

        viewModelScope.launch {
            try {
                AppDatabase.Companion
                    .getInstance()
                    .getUserRepository()
                    .watchIsDelete()
                    .collect {
                        if (it == 1L) {
                            // logout
                            _isDelete.value = 1L
                        } else {
                            _isDelete.value = 0L
                        }
                    }
            } catch (e: Exception) {
                debugLog("Connection error: ${e.message}")
            }
        }
    }

    private val _isShowCoinInsufficientDialog = MutableStateFlow(false)
    val isShowCoinInsufficientDialog = _isShowCoinInsufficientDialog.asStateFlow()

    fun showInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = true
    }

    fun hideInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = false
    }

    val appThemeMode: MutableLiveData<AppThemeMode> by lazy {
        val savedAppThemeMode = PrefAssist.getString(
            PrefConst.APP_THEME_MODE,
            AppThemeMode.SYSTEM.name
        )
        MutableLiveData<AppThemeMode>(AppThemeMode.valueOf(savedAppThemeMode))
    }


    fun setAppTheme(appThemeMode: AppThemeMode) {
        this.appThemeMode.value = appThemeMode
        PrefAssist.setString(PrefConst.APP_THEME_MODE, appThemeMode.name)
    }


    private val _coinTotal = MutableStateFlow(PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0))
    val coinTotal = _coinTotal.asStateFlow()

    private val _isDelete = MutableStateFlow(0L)
    val isDelete = _isDelete.asStateFlow()

    fun setCoinBalance(newCoinTotal: Long) {
        _coinTotal.value = newCoinTotal
        PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, newCoinTotal)

        if (FirebaseAuth.getInstance().currentUser?.uid == null) {
            PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_TEMP, newCoinTotal)
        }

        debugLog("saveCoinTotal: $newCoinTotal")
    }

    fun updateCoinBalance(amount: Long, description: String) {
        val coinTotal = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
        viewModelScope.launch(Dispatchers.IO) {
            if (FirestoreUtils.syncCoinsInterval % 10 != 0L) {
                FirestoreUtils.syncCoinsFirestore()
            }
            FirestoreUtils.syncCoinsInterval++

            withContext(Dispatchers.Main) {
                setCoinBalance(coinTotal + amount)
            }

            val user = FirebaseAuth.getInstance().currentUser
            if (user?.uid != null){
                AppDatabase
                    .getInstance()
                    .getUserRepository()
                    .updateCoin(
                        coinTotal + amount
                    )

            }
            debugLog( "updateCoinBalance: $amount, description: $description")
            AppDatabase
                .getInstance()
                .getCoinHistoryRepository()
                .insertTransaction(
                    CoinHistoryEntityPowerSync(
                        type = if (amount > 0) TransactionType.EARN else TransactionType.SPEND,
                        amount = abs(amount.toInt()),
                        date = System.currentTimeMillis(),
                        description = description
                    )
                )
        }
    }


    private val _freeChat = MutableStateFlow(PrefAssist.getInt(PrefConst.FREE_CHAT))
    val freeChat = _freeChat.asStateFlow()

    fun countdownFreeChat() {
        val newFreeChat = PrefAssist.getInt(PrefConst.FREE_CHAT) - 1
        _freeChat.value = newFreeChat
    }

}